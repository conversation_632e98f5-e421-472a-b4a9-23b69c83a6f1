import { Component, Input, ViewChild, ElementRef, OnInit, Output, EventEmitter } from '@angular/core';
import { ToastrService } from "ngx-toastr";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ManagedAccountService } from '../../managed-account.service';
import { TableFootnoteModel } from '../../models/table-footnote.model';
import { DataTableFootnoteComponent } from './data-table-footnote/data-table-footnote.component';

import { DownloadTemplate } from '../../models/commentary.model';
import { MiscellaneousService } from 'src/app/services/miscellaneous.service';
import { UploadDataModalComponent } from '../../../shared/upload-data-modal/upload-data-modal.component';

@Component({
  selector: 'app-managed-account-data-table',
  templateUrl: './managed-account-data-table.component.html',
  styleUrls: ['./managed-account-data-table.component.scss']
})
export class ManagedAccountDataTableComponent implements OnInit {
  @Input() managedAccountId: string = '';
  @Input() tableTitle: string = '';
  @Input() isStaticTable: boolean = false;
  @Input() tableName: string = '';
  @Input() data: any[] = [];
  @Input() permissions: { [subFeature: string]: { canView: boolean; canEdit: boolean; canImport?: boolean; canExport?: boolean } };
 
  footnotes = [
    { name: "FootNote", newComment: "", isExpanded: false, isEdit: false },
  ];
  @Input() companyID: string = '';
  @Input() moduleName: string = '';
  @Output() dataUploaded = new EventEmitter<any>();
  @ViewChild('fileInput', { static: false }) fileInput!: ElementRef;

 

  menuItems: any[] = [{ text: 'Delete Table'}];
  isMenuOpen:boolean = false;
  showPopup:boolean = false;
  openUploadDialog: boolean = false;
  @ViewChild('anchor', { static: true }) anchor: ElementRef;
  periodData: any[] = [];

  // Properties for managing cap table configuration
  capTableConfig: any = null;
  hasCapTableData: boolean = false;
  isLoadingConfig: boolean = false;
  periodId: number = 0;

  // Properties for period selection
  selectedPeriod: any = null;

  constructor(
    private managedAccountService: ManagedAccountService,
    private _miscService: MiscellaneousService,
    private modalService: NgbModal,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    if (this.tableTitle && this.managedAccountId) {
        this.loadFootnote();
    }

    // Load cap table configuration when component initializes
    if (this.managedAccountId && this.moduleName) {
      this.loadCapTableConfig();
    }
  }

  // File handling methods
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.handleFileUpload(file);
    }
  }

  openUpload(): void {
    if (!this.permissions?.[this.tableName]?.canImport) {
      this.toastrService.error('You do not have permission to import data', '', {
        positionClass: 'toast-center-center',
      });
      return;
    }

    const dialogRef = this.modalService.open(UploadDataModalComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    dialogRef.componentInstance.tableName = this.tableName;
    dialogRef.componentInstance.companyID = this.companyID;
    dialogRef.componentInstance.moduleName = this.moduleName;

    dialogRef.componentInstance.downloadTemplate.subscribe(() => {
      this.downloadTemplate();
    });

    dialogRef.componentInstance.uploadSuccess.subscribe((response: any) => {
      this.handleUploadSuccess(response);
    });
  }

  handleFileUpload(file: File): void {
    if (!file) return;

    // Security check for file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const allowedTypes = ['xlsx', 'xls'];

    if (!allowedTypes.includes(fileExtension || '')) {
      this.toastrService.error('Invalid file type. Please upload Excel files only.');
      return;
    }

    // File size check (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      this.toastrService.error('File size exceeds 5MB limit');
      return;
    }

    // TODO: Implement actual file upload to server
    // This would typically call a service method to upload the file
    console.log('Uploading file:', file);
    this.toastrService.success(`File ${file.name} uploaded successfully`, '', {
      positionClass: 'toast-center-center'
    });
  }

  handleUploadSuccess(response: any): void {
    // Emit event to parent component to refresh data or perform other actions
    this.dataUploaded.emit(response);

    // You can add additional logic here such as:
    // - Refreshing the table data
    // - Showing success messages
    // - Updating UI state
    console.log('Upload successful:', response);
  }

  downloadTemplate(): void {
    if (!this.companyID || !this.moduleName) {
      console.error('Company ID and Module ID are required for template download');
      return;
    }
    const template: DownloadTemplate = { managedAccountId: this.companyID, moduleName: this.moduleName };
    this.managedAccountService.downloadTemplate(template).subscribe({
      next: (response) => {
        // Create blob from response
        this._miscService.downloadExcelFile(response);
      },
      error: (error) => {
        console.error('Error downloading template:', error);
        // You can add user-friendly error handling here
      }
    });
  }


  // Export functionality
  exportToExcel(): void {
  }

  //threeDots delete menu handling methods
  openMenu(event: any): void {
    this.isMenuOpen = !this.isMenuOpen;
  }

  onSelect(event: any): void {
    if (event.item.text === 'Delete Table') {
      this.showDeletePopup();
    }
    this.isMenuOpen = false;
  }

  showDeletePopup() {
    this.showPopup = true;
  }
  hideDeletePopup() {
    this.showPopup = false;
    this.isMenuOpen = false;
  }
  deleteItem() {
    console.log('Delete item');
  }
  deleteError(){
    this.isMenuOpen = false;
    this.hideDeletePopup();
    this.toastrService.error("Error while deleting data", "", {
      positionClass: "toast-center-center",
    });
    // this.reloadData();  
  }

  handleSave(footnote: any): void {
    const footnoteContent = footnote.newComment || "";
    const footnotePayload: TableFootnoteModel = {
      footnoteId: 0, 
      footnoteMapping: this.tableName,
      footnoteIdentifier: String(this.managedAccountId),
      footnoteContent: footnoteContent, 
    };

    this.managedAccountService.saveFootnote(footnotePayload).subscribe({
      next: (response) => {
        this.toastrService.success("Footnote saved successfully", "", {
          positionClass: "toast-center-center",
        });
        footnote.isEdit = false;
      },
      error: (error) => {
        this.toastrService.error("Error saving footnote");
      },
    });
  }

  handleCancel(footnote: any): void {
    footnote.isEdit = false;
    this.loadFootnote();
  }

  handleReset(footnote: any): void {
    footnote.newComment = "";
  }

  private loadFootnote(): void {
    let footnoteModel: TableFootnoteModel = {
      footnoteId: 0,
      footnoteMapping: this.tableName,
      footnoteIdentifier: this.managedAccountId,
      footnoteContent: ''
    };

    this.managedAccountService
      .getFootnote(footnoteModel)
      .subscribe({
        next: (response) => {
          if (response && response.footnoteContent) {
            // Update the footnote content in the existing footnotes array
            if (this.footnotes.length > 0) {
              this.footnotes[0].newComment = response.footnoteContent;
              this.footnotes[0].isExpanded =
                response.footnoteContent?.trim().length > 0;
            }
          }
        },
        error: (error) => {
          this.toastrService.error("Error loading footnote");
        },
      });
  }

  private loadCapTableConfig(): void {
    this.isLoadingConfig = true;

    this.managedAccountService.getManagedCapTableConfig(this.managedAccountId, this.moduleName)
      .subscribe({
        next: (response) => {
          this.capTableConfig = response;
          this.hasCapTableData = response && response.capTablePeriods && response.capTablePeriods.length > 0;

          // Initialize period data
          if (response && response.capTablePeriods) {
            this.periodData = response.capTablePeriods;
          }

          // Set selectedPeriod to latestPeriod from API response
          if (this.hasCapTableData && response.latestPeriod) {
            this.selectedPeriod = response.latestPeriod;
            this.periodId = response.latestPeriod.periodId;
          } else {
            // No latest period available - clear selection
            this.selectedPeriod = null;
            this.periodId = 0;
          }

          this.isLoadingConfig = false;
        },
        error: (error) => {
          console.error('Error loading cap table configuration:', error);
          this.hasCapTableData = false;
          this.selectedPeriod = null;
          this.periodId = 0;
          this.isLoadingConfig = false;
        }
      });
  }

  // Methods for period selection

  onPeriodSelect(event: any): void {
    this.selectedPeriod = event;
    this.periodId = event?.periodId || 0;   
  }

  // Getter for combobox disabled state
  get isPeriodComboboxDisabled(): boolean {
    return !this.periodData || this.periodData.length === 0;
  }

}
