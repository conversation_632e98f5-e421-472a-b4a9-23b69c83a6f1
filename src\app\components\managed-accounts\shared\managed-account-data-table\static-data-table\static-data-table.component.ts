import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';

@Component({
  selector: 'app-static-data-table',
  templateUrl: './static-data-table.component.html',
  styleUrls: ['./static-data-table.component.scss']
})
export class StaticDataTableComponent implements OnChanges {
  @Input() periodId: number = 0;
  @Input() isMonthly: boolean = false;
  @Input() isQuarterly: boolean = false;
  @Input() isAnnually: boolean = false;

  // Add any data properties needed for the static table
  tableData: any[] = [];
  loading: boolean = false;

  constructor() {}
  

  ngOnChanges(changes: SimpleChanges): void {
    // Reload data when any of the period-related inputs change
    if (changes['periodId'] || changes['isMonthly'] || changes['isQuarterly'] || changes['isAnnually']) {
      this.loadStaticTableData();
    }
  }

  private loadStaticTableData(): void {
    // Only load data if we have a valid periodId
    if (this.periodId > 0) {
      this.loading = true;

      // TODO: Implement actual API call for static table data
      // This would typically call a service method to load static table data
      // based on the period parameters

      console.log('Loading static table data with:', {
        periodId: this.periodId,
        isMonthly: this.isMonthly,
        isQuarterly: this.isQuarterly,
        isAnnually: this.isAnnually
      });

      // Simulate loading delay
      setTimeout(() => {
        this.tableData = []; // Replace with actual data from API
        this.loading = false;
      }, 500);
    } else {
      this.tableData = [];
      this.loading = false;
    }
  }
}
